package config

import (
	"gitlab.tpo.xzoa.com/tpo-public/common/remotecfg"
	"gitlab.tpo.xzoa.com/tpo-public/logger"
	"log"
	"sync/atomic"
)

const (
	appID     = "kingdee"
	namespace = "server.yaml"
)

var cfg *atomic.Value

func Init(env, url string) {
	if env == "" || url == "" {
		logger.Fatalf("env or url is empty")
	}
	var err error
	cfg, err = remotecfg.NewApollo(url, appID, env).InitAndWatchYamlCfg(namespace, &Config{})
	if err != nil {
		log.Fatalf("load config error: %v", err)
	}
}

func GetKingdeeApp() KingdeeApp {
	return cfg.Load().(*Config).KingdeeApp
}

type Config struct {
	KingdeeApp KingdeeApp `yaml:"kingdeeApp"`
}

type KingdeeApp struct {
	AccountID     string `yaml:"accountId"`     // 1565321489509515264
	AppID         string `yaml:"appId"`         // xiaozhi
	<PERSON>pp<PERSON>ey        string `yaml:"appKey"`        // 45dd6aa5-49e9-4024-BE44-39c934c5038c
	XAcgwIdentity string `yaml:"xAcgwIdentity"` // djF8MTk5NTU5NzRmNzcwMWRlZWU3MDF8NDkxMTY3NzYwNDUzNHwdqTqz5YoF8N-dfRyI1V48q5caM6jn-Fc46_a0N7v023w=
}
