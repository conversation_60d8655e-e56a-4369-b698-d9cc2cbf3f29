package config

import (
	"gitlab.tpo.xzoa.com/tpo-public/common/remotecfg"
	"gitlab.tpo.xzoa.com/tpo-public/logger"
	"log"
	"sync/atomic"
)

const (
	appID     = "kingdee"
	namespace = "server.yaml"
)

var cfg *atomic.Value

func Init(env, url string) {
	if env == "" || url == "" {
		logger.Fatalf("env or url is empty")
	}
	var err error
	cfg, err = remotecfg.NewApollo(url, appID, env).InitAndWatchYamlCfg(namespace, &Config{})
	if err != nil {
		log.Fatalf("load config error: %v", err)
	}
}

func GetKingdeeApp() KingdeeApp {
	return cfg.Load().(*Config).KingdeeApp
}

func GetMySQL() MySQL {
	return cfg.Load().(*Config).MySQL
}

type Config struct {
	KingdeeApp KingdeeApp `yaml:"kingdeeApp"`
	MySQL      MySQL      `yaml:"mysql"`
}

type KingdeeApp struct {
	AccountID     string `yaml:"accountId"`     // 1565321489509515264
	AppID         string `yaml:"appId"`         // xiaozhi
	AppKey        string `yaml:"appKey"`        // 45dd6aa5-49e9-4024-BE44-39c934c5038c
	XAcgwIdentity string `yaml:"xAcgwIdentity"` // djF8MTk5NTU5NzRmNzcwMWRlZWU3MDF8NDkxMTY3NzYwNDUzNHwdqTqz5YoF8N-dfRyI1V48q5caM6jn-Fc46_a0N7v023w=
}

type MySQL struct {
	Host            string `yaml:"host"`            // 数据库主机地址
	Port            int    `yaml:"port"`            // 数据库端口
	Username        string `yaml:"username"`        // 数据库用户名
	Password        string `yaml:"password"`        // 数据库密码
	Database        string `yaml:"database"`        // 数据库名称
	Charset         string `yaml:"charset"`         // 字符集
	MaxIdleConns    int    `yaml:"maxIdleConns"`    // 最大空闲连接数
	MaxOpenConns    int    `yaml:"maxOpenConns"`    // 最大打开连接数
	ConnMaxLifetime int    `yaml:"connMaxLifetime"` // 连接最大生存时间(分钟)
}
