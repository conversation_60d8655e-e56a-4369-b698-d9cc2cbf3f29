# 示例配置文件 - 用于Apollo配置中心
# 实际使用时，请将此配置添加到Apollo配置中心的server.yaml命名空间

kingdeeApp:
  accountId: "1565321489509515264"
  appId: "xiaozhi"
  appKey: "45dd6aa5-49e9-4024-BE44-39c934c5038c"
  xAcgwIdentity: "djF8MTk5NTU5NzRmNzcwMWRlZWU3MDF8NDkxMTY3NzYwNDUzNHwdqTqz5YoF8N-dfRyI1V48q5caM6jn-Fc46_a0N7v023w="

mysql:
  host: "localhost"           # 数据库主机地址
  port: 3306                  # 数据库端口
  username: "root"            # 数据库用户名
  password: "password"        # 数据库密码
  database: "kingdee"         # 数据库名称
  charset: "utf8mb4"          # 字符集
  maxIdleConns: 10            # 最大空闲连接数
  maxOpenConns: 100           # 最大打开连接数
  connMaxLifetime: 60         # 连接最大生存时间(分钟)

# 生产环境配置示例
# mysql:
#   host: "prod-mysql.example.com"
#   port: 3306
#   username: "kingdee_user"
#   password: "your_secure_password"
#   database: "kingdee_prod"
#   charset: "utf8mb4"
#   maxIdleConns: 20
#   maxOpenConns: 200
#   connMaxLifetime: 120
