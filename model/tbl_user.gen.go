// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTblUser = "tbl_user"

// TblUser mapped from table <tbl_user>
type TblUser struct {
	ID           int32     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                 // 主键ID
	XzAccountID  int64     `gorm:"column:xz_account_id;not null;comment:小智账号ID" json:"xz_account_id"`              // 小智账号ID
	KdAccountID  string    `gorm:"column:kd_account_id;not null;comment:金蝶账号ID" json:"kd_account_id"`              // 金蝶账号ID
	Number       string    `gorm:"column:number;not null;comment:金蝶工号" json:"number"`                              // 金蝶工号
	Name         string    `gorm:"column:name;not null;comment:姓名" json:"name"`                                    // 姓名
	Username     string    `gorm:"column:username;not null;comment:用户名" json:"username"`                           // 用户名
	Phone        string    `gorm:"column:phone;not null;comment:手机号" json:"phone"`                                 // 手机号
	Gender       int32     `gorm:"column:gender;not null;comment:性别 [1:男, 2:女, 0:保密]" json:"gender"`               // 性别 [1:男, 2:女, 0:保密]
	Idcard       string    `gorm:"column:idcard;not null;comment:身份证号" json:"idcard"`                              // 身份证号
	Email        string    `gorm:"column:email;not null;comment:邮箱" json:"email"`                                  // 邮箱
	PicturefieID string    `gorm:"column:picturefieId;not null;comment:人员头像，完整url" json:"picturefieId"`            // 人员头像，完整url
	UserTypes    string    `gorm:"column:user_types;not null;comment:用户类型，金蝶那边的完整结构" json:"user_types"`            // 用户类型，金蝶那边的完整结构
	UserTypeID   int32     `gorm:"column:user_type_id;not null;comment:用户类型ID，单独提出一个枚举字段方便查询" json:"user_type_id"` // 用户类型ID，单独提出一个枚举字段方便查询
	CreatedAt    time.Time `gorm:"column:created_at;not null;comment:创建时间" json:"created_at"`                      // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;not null;comment:更新时间" json:"updated_at"`                      // 更新时间
}

// TableName TblUser's table name
func (*TblUser) TableName() string {
	return TableNameTblUser
}
