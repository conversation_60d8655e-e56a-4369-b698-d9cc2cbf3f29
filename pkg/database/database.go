package database

import (
	"fmt"
	"time"

	"gitlab.tpo.xzoa.com/tpo/kingdee/server/config"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDB 初始化数据库连接
func InitDB() (*gorm.DB, error) {
	// 从远程配置获取数据库配置
	dbConfig := config.GetMySQL()

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database,
		dbConfig.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层的sql.DB对象进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	maxIdleConns := dbConfig.MaxIdleConns
	if maxIdleConns <= 0 {
		maxIdleConns = 10 // 默认值
	}

	maxOpenConns := dbConfig.MaxOpenConns
	if maxOpenConns <= 0 {
		maxOpenConns = 100 // 默认值
	}

	connMaxLifetime := dbConfig.ConnMaxLifetime
	if connMaxLifetime <= 0 {
		connMaxLifetime = 60 // 默认60分钟
	}

	sqlDB.SetMaxIdleConns(maxIdleConns)
	sqlDB.SetMaxOpenConns(maxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(connMaxLifetime) * time.Minute)

	DB = db
	return db, nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}
