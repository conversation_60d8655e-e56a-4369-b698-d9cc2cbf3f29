package api

import (
	"github.com/gin-gonic/gin"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/handler"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/service"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/repository"
	"gorm.io/gorm"
)

// SetupRoutes 设置路由
// 这个函数可以被外部调用来设置路由
func SetupRoutes(router *gin.Engine, db *gorm.DB) {
	// 初始化仓储层
	repos := repository.NewRepositories(db)

	// 初始化服务层
	services := service.NewServices(repos)

	// 初始化处理器
	handlers := handler.NewHandlers(services)

	// 注册路由
	handlers.RegisterRoutes(router)
}
