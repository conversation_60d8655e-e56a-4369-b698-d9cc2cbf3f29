# Kingdee Server

基于Gin框架的HTTP服务器，使用MySQL数据库和GORM ORM框架。

## 项目结构

```
kingdee/server/
├── cmd/                    # 主要应用程序入口
│   └── server/
│       └── main.go
├── internal/               # 私有应用程序和库代码
│   ├── app/               # 应用程序组装
│   ├── handler/           # HTTP处理器
│   ├── service/           # 业务逻辑层
│   ├── repository/        # 数据访问层
│   └── middleware/        # 中间件
├── pkg/                   # 可被外部应用程序使用的库代码
│   ├── database/          # 数据库连接和配置
│   ├── response/          # 统一响应格式
│   └── utils/             # 工具函数
├── model/                 # GORM生成的数据模型
├── query/                 # GORM gen生成的查询代码
├── migration/             # 数据库迁移文件
├── api/                   # API定义和路由
├── config/                # 配置相关
├── scripts/               # 构建、安装、分析等脚本
├── tools/                 # 工具和辅助程序
│   └── gen/               # GORM gen代码生成工具
├── docs/                  # 设计和用户文档
├── docker/                # Docker相关文件
├── .gitignore
├── Makefile
├── README.md
├── go.mod
└── go.sum
```

## 技术栈

- **Web框架**: Gin
- **数据库**: MySQL 8.0
- **ORM**: GORM v2
- **代码生成**: GORM Gen
- **配置管理**: Apollo (远程配置)
- **日志**: 自定义日志库
- **容器化**: Docker & Docker Compose

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Docker (可选)

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd kingdee/server
```

2. 安装依赖
```bash
make deps
```

3. 配置数据库
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE kingdee CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行迁移
mysql -u root -p kingdee < migration/001_create_users_table.sql
```

4. 生成GORM代码
```bash
make gen
```

5. 运行应用
```bash
make run
```

### 使用Docker

1. 启动所有服务
```bash
docker-compose up -d
```

2. 查看日志
```bash
docker-compose logs -f app
```

## API文档

### 用户管理

#### 创建用户
```
POST /api/v1/users
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 获取用户信息
```
GET /api/v1/users/{id}
```

#### 更新用户信息
```
PUT /api/v1/users/{id}
Content-Type: application/json

{
  "username": "newusername",
  "email": "<EMAIL>",
  "status": 1
}
```

#### 删除用户
```
DELETE /api/v1/users/{id}
```

#### 获取用户列表
```
GET /api/v1/users?page=1&size=10
```

#### 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

### 健康检查
```
GET /health
```

## 开发指南

### 添加新的API

1. 在`model/`目录下定义数据模型
2. 运行`make gen`生成查询代码
3. 在`internal/repository/`下实现数据访问层
4. 在`internal/service/`下实现业务逻辑层
5. 在`internal/handler/`下实现HTTP处理器
6. 在相应的handler中注册路由

### 数据库迁移

1. 在`migration/`目录下创建SQL文件
2. 按照命名规范：`{序号}_{描述}.sql`
3. 运行迁移命令

### 代码生成

使用GORM Gen自动生成模型和查询代码：

```bash
# 修改 tools/gen/main.go 中的数据库连接信息
# 然后运行
make gen
```

## 部署

### 生产环境构建

```bash
make prod
```

### Docker部署

```bash
# 构建镜像
make docker-build

# 推送镜像
make docker-push

# 运行容器
make docker-run
```

## 配置

项目使用Apollo作为配置中心，配置文件结构如下：

```yaml
kingdeeApp:
  accountId: "1565321489509515264"
  appId: "xiaozhi"
  appKey: "45dd6aa5-49e9-4024-BE44-39c934c5038c"
  xAcgwIdentity: "djF8MTk5NTU5NzRmNzcwMWRlZWU3MDF8NDkxMTY3NzYwNDUzNHwdqTqz5YoF8N-dfRyI1V48q5caM6jn-Fc46_a0N7v023w="

mysql:
  host: "localhost"
  port: 3306
  username: "root"
  password: "password"
  database: "kingdee"
  charset: "utf8mb4"
  maxIdleConns: 10        # 最大空闲连接数
  maxOpenConns: 100       # 最大打开连接数
  connMaxLifetime: 60     # 连接最大生存时间(分钟)
```

## 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

[MIT License](LICENSE)
