# Makefile for Kingdee Server

# 变量定义
APP_NAME=kingdee-server
BUILD_DIR=build
CMD_DIR=cmd/server
DOCKER_IMAGE=kingdee/server
VERSION?=latest

# Go相关变量
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 构建应用
.PHONY: build
build:
	mkdir -p $(BUILD_DIR)
	$(GOBUILD) -o $(BUILD_DIR)/$(APP_NAME) ./$(CMD_DIR)

# 构建Linux版本
.PHONY: build-linux
build-linux:
	mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BUILD_DIR)/$(APP_NAME)-linux ./$(CMD_DIR)

# 运行应用
.PHONY: run
run:
	$(GOCMD) run ./$(CMD_DIR) -env=dev -url=http://localhost:8080

# 运行测试
.PHONY: test
test:
	$(GOTEST) -v ./...

# 运行测试并生成覆盖率报告
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html

# 清理构建文件
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 格式化代码
.PHONY: fmt
fmt:
	$(GOCMD) fmt ./...

# 代码检查
.PHONY: vet
vet:
	$(GOCMD) vet ./...

# 生成GORM代码
.PHONY: gen
gen:
	$(GOCMD) run ./tools/gen -env=dev -url=http://localhost:8080

# Docker相关命令
.PHONY: docker-build
docker-build:
	docker build -t $(DOCKER_IMAGE):$(VERSION) .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 $(DOCKER_IMAGE):$(VERSION)

.PHONY: docker-push
docker-push:
	docker push $(DOCKER_IMAGE):$(VERSION)

# 数据库迁移
.PHONY: migrate-up
migrate-up:
	@echo "Running database migrations..."
	# 这里可以添加数据库迁移命令

.PHONY: migrate-down
migrate-down:
	@echo "Rolling back database migrations..."
	# 这里可以添加数据库回滚命令

# 开发环境启动
.PHONY: dev
dev: deps gen
	$(GOCMD) run ./$(CMD_DIR) -env=dev -url=http://localhost:8080

# 生产环境构建
.PHONY: prod
prod: clean deps test build-linux

# 帮助信息
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  all          - Clean, install deps and build"
	@echo "  deps         - Install dependencies"
	@echo "  build        - Build the application"
	@echo "  build-linux  - Build for Linux"
	@echo "  run          - Run the application"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  clean        - Clean build files"
	@echo "  fmt          - Format code"
	@echo "  vet          - Run go vet"
	@echo "  gen          - Generate GORM code"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  docker-push  - Push Docker image"
	@echo "  dev          - Start development environment"
	@echo "  prod         - Production build"
	@echo "  help         - Show this help"
