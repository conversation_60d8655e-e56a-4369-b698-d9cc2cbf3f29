CREATE TABLE `tbl_user` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `xz_account_id` bigint unsigned NOT NULL COMMENT '小智账号ID',
    `kd_account_id` varchar(32) NOT NULL COMMENT '金蝶账号ID',
    `number` varchar(16) NOT NULL COMMENT '金蝶工号',
    `name` varchar(16) NOT NULL COMMENT '姓名',
    `username` varchar(32) NOT NULL COMMENT '用户名',
    `phone` varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
    `gender` tinyint(3) unsigned zerofill NOT NULL COMMENT '性别 [1:男, 2:女, 0:保密]',
    `idcard` varchar(32) NOT NULL DEFAULT '' COMMENT '身份证号',
    `email` varchar(255) NOT NULL DEFAULT '' COMMENT '邮箱',
    `picturefieId` varchar(255) NOT NULL DEFAULT '' COMMENT '人员头像，完整url',
    `user_types` varchar(255) NOT NULL COMMENT '用户类型，金蝶那边的完整结构',
    `user_type_id` int NOT NULL COMMENT '用户类型ID，单独提出一个枚举字段方便查询',
    `created_at` datetime NOT NULL COMMENT '创建时间',
    `updated_at` datetime NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;