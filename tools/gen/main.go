package main

import (
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// GORM gen代码生成工具
func main() {
	// 连接数据库
	dsn := "root:tz@tcp(10.234.4.124:31107)/db_kingdee?charset=utf8&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// 创建gen实例
	g := gen.NewGenerator(gen.Config{
		OutPath:       "query", // 输出路径
		Mode:          gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldNullable: true,
	})

	// 使用数据库连接
	g.UseDB(db)

	// 生成所有表的模型
	g.ApplyBasic(
		// 从数据库生成所有表
		g.GenerateAllTable()...,
	)

	// 执行生成
	g.Execute()
}
