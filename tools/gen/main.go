package main

import (
	"flag"
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/config"
	"gitlab.tpo.xzoa.com/tpo-public/logger"
)

var (
	env = flag.String("env", "dev", "environment")
	url = flag.String("url", "", "apollo config url")
)

// GORM gen代码生成工具
func main() {
	flag.Parse()

	// 初始化配置
	config.Init(*env, *url)

	// 初始化日志
	logger.Init()

	// 从远程配置获取数据库配置
	dbConfig := config.GetMySQL()

	// 连接数据库
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		dbConfig.Username,
		dbConfig.Password,
		dbConfig.Host,
		dbConfig.Port,
		dbConfig.Database,
		dbConfig.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("failed to connect database: %v", err)
	}

	// 创建gen实例
	g := gen.NewGenerator(gen.Config{
		OutPath:       "../../query", // 输出路径
		Mode:          gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldNullable: true,
	})

	// 使用数据库连接
	g.UseDB(db)

	// 生成所有表的模型
	g.ApplyBasic(
		// 从数据库生成所有表
		g.GenerateAllTable()...,
	)

	// 执行生成
	g.Execute()
}
