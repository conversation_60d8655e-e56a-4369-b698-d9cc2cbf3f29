package app

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/handler"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/middleware"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/repository"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/service"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/pkg/database"
	"gitlab.tpo.xzoa.com/tpo-public/logger"
)

type App struct {
	server *http.Server
}

func NewApp() *App {
	return &App{}
}

func (a *App) Run() error {
	// 初始化数据库
	db, err := database.InitDB()
	if err != nil {
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	// 初始化仓储层
	repos := repository.NewRepositories(db)

	// 初始化服务层
	services := service.NewServices(repos)

	// 初始化处理器
	handlers := handler.NewHandlers(services)

	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	// 创建路由
	router := gin.New()
	
	// 添加中间件
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.CORS())

	// 注册路由
	handlers.RegisterRoutes(router)

	// 创建HTTP服务器
	a.server = &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// 启动服务器
	go func() {
		logger.Infof("Server starting on port 8080")
		if err := a.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Server shutting down...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := a.server.Shutdown(ctx); err != nil {
		logger.Errorf("Server forced to shutdown: %v", err)
		return err
	}

	logger.Info("Server exited")
	return nil
}
