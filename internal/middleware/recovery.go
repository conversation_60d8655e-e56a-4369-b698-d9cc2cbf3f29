package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/pkg/response"
	"gitlab.tpo.xzoa.com/tpo-public/logger"
)

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		logger.Errorf("Panic recovered: %v", recovered)
		response.ServerError(c, "Internal server error")
		c.AbortWithStatus(http.StatusInternalServerError)
	})
}
