package handler

import (
	"github.com/gin-gonic/gin"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/service"
)

// Handlers 处理器集合
type Handlers struct {
	User *UserHandler
	// 在这里添加其他处理器
}

// NewHandlers 创建处理器实例
func NewHandlers(services *service.Services) *Handlers {
	return &Handlers{
		User: NewUserHandler(services.User),
		// 在这里初始化其他处理器
	}
}

// RegisterRoutes 注册所有路由
func (h *Handlers) RegisterRoutes(router *gin.Engine) {
	// API版本分组
	v1 := router.Group("/api/v1")
	
	// 用户相关路由
	h.User.RegisterRoutes(v1)
	
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "Server is running",
		})
	})
}
