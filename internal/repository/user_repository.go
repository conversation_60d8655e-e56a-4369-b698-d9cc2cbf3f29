package repository

import (
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/model"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/query"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	Create(user *model.User) error
	GetByID(id uint) (*model.User, error)
	GetByUsername(username string) (*model.User, error)
	GetByEmail(email string) (*model.User, error)
	Update(user *model.User) error
	Delete(id uint) error
	List(offset, limit int) ([]*model.User, int64, error)
}

// userRepository 用户仓储实现
type userRepository struct {
	query *query.Query
}

// NewUserRepository 创建用户仓储实例
func NewUserRepository(q *query.Query) UserRepository {
	return &userRepository{
		query: q,
	}
}

// Create 创建用户
func (r *userRepository) Create(user *model.User) error {
	return r.query.User.Create(user)
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id uint) (*model.User, error) {
	return r.query.User.GetByID(id)
}

// GetByUsername 根据用户名获取用户
func (r *userRepository) GetByUsername(username string) (*model.User, error) {
	return r.query.User.GetByUsername(username)
}

// GetByEmail 根据邮箱获取用户
func (r *userRepository) GetByEmail(email string) (*model.User, error) {
	// 这里需要在query中添加GetByEmail方法
	// 暂时使用GetByUsername作为示例
	return r.query.User.GetByUsername(email)
}

// Update 更新用户
func (r *userRepository) Update(user *model.User) error {
	return r.query.User.Update(user)
}

// Delete 删除用户
func (r *userRepository) Delete(id uint) error {
	return r.query.User.Delete(id)
}

// List 获取用户列表
func (r *userRepository) List(offset, limit int) ([]*model.User, int64, error) {
	return r.query.User.List(offset, limit)
}
