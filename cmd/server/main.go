package main

import (
	"flag"
	"log"

	"gitlab.tpo.xzoa.com/tpo-public/logger"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/config"
	"gitlab.tpo.xzoa.com/tpo/kingdee/server/internal/app"
)

var (
	env = flag.String("env", "dev", "environment")
	url = flag.String("url", "", "apollo config url")
)

func main() {
	flag.Parse()

	// 初始化配置
	config.Init(*env, *url)

	// 初始化日志
	logger.Init()

	// 启动应用
	app := app.NewApp()
	if err := app.Run(); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
