# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/
dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Environment files
.env
.env.local
.env.*.local

# Coverage files
coverage.out
coverage.html

# Temporary files
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config/local.yaml
config/production.yaml

# Docker volumes
docker/mysql/data/
docker/redis/data/

# Generated files
query/
!query/.gitkeep

# Air live reload
tmp/
